let canvas, ctx, isDrawing = false;
let lastX = 0, lastY = 0;

window.initializeSignaturePad = () => {
    canvas = document.getElementById('signatureCanvas');
    if (!canvas) return;
    
    ctx = canvas.getContext('2d');
    
    // Set up canvas properties
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // Mouse events
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);
    
    // Touch events for mobile
    canvas.addEventListener('touchstart', handleTouch);
    canvas.addEventListener('touchmove', handleTouch);
    canvas.addEventListener('touchend', stopDrawing);
    
    // Prevent scrolling when touching the canvas
    canvas.addEventListener('touchstart', preventDefault, { passive: false });
    canvas.addEventListener('touchend', preventDefault, { passive: false });
    canvas.addEventListener('touchmove', preventDefault, { passive: false });
};

function startDrawing(e) {
    isDrawing = true;
    const rect = canvas.getBoundingClientRect();
    lastX = e.clientX - rect.left;
    lastY = e.clientY - rect.top;
    hideSignaturePlaceholder();
}

function draw(e) {
    if (!isDrawing) return;
    
    const rect = canvas.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;
    
    ctx.beginPath();
    ctx.moveTo(lastX, lastY);
    ctx.lineTo(currentX, currentY);
    ctx.stroke();
    
    lastX = currentX;
    lastY = currentY;
}

function stopDrawing() {
    isDrawing = false;
}

function handleTouch(e) {
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                     e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    canvas.dispatchEvent(mouseEvent);
}

function preventDefault(e) {
    e.preventDefault();
}

function hideSignaturePlaceholder() {
    const container = canvas.parentElement;
    container.classList.add('has-signature');
}

function showSignaturePlaceholder() {
    const container = canvas.parentElement;
    container.classList.remove('has-signature');
}

window.clearSignature = () => {
    if (canvas && ctx) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        showSignaturePlaceholder();
    }
};

window.getSignatureData = () => {
    if (canvas) {
        // Check if canvas is blank
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const isBlank = !imageData.data.some(channel => channel !== 0);
        
        if (isBlank) {
            return '';
        }
        
        return canvas.toDataURL('image/png');
    }
    return '';
};
