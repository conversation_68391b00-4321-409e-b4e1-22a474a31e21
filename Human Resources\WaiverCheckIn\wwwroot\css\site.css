/* Landing Page Styles */
.landing-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
    text-align: center;
    padding: 2rem;
}

.logo-container {
    margin-bottom: 2rem;
}

.logo-placeholder {
    width: 200px;
    height: 100px;
    background-color: #f0f0f0;
    border: 2px dashed #ccc;
    border-radius: 8px;
    object-fit: contain;
}

.title-container {
    margin-bottom: 3rem;
}

.main-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.button-container {
    margin-top: 1rem;
}

.check-in-button {
    padding: 1rem 2rem;
    font-size: 1.25rem;
    border-radius: 8px;
}

/* Check-In Form Styles */
.checkin-container {
    max-width: 700px;
    margin: 2rem auto;
    padding: 2rem;
    background-color: #000000;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-title {
    text-align: center;
    margin-bottom: 2rem;
    color: #ffffff;
}

.form-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-title {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
    border-bottom: 2px solid #7e132a;
    padding-bottom: 0.5rem;
}

.checkin-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #555;
}

.form-control {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    width: auto;
    margin: 0;
}

.form-check-label {
    margin: 0;
    font-weight: normal;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}
