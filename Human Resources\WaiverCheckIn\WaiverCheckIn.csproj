<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.15.0" />
    <PackageReference Include="Blazorise.Bootstrap" Version="1.8.1" />
    <PackageReference Include="Blazorise.SignaturePad" Version="1.8.1" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.19" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.19" PrivateAssets="all" />
    <PackageReference Include="Microsoft.Graph" Version="5.91.0" />
  </ItemGroup>

</Project>
