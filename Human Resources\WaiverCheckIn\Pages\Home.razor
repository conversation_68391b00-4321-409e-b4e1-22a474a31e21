﻿@page "/"
@inject NavigationManager Navigation

<PageTitle>Job <PERSON> Waiver Check-In</PageTitle>

<div class="landing-page">
    <div class="logo-container">
        <img src="~/stc-blacklogo-2024.png" alt="Company Logo" class="logo-placeholder" />
    </div>

    <div class="title-container">
        <h1 class="main-title">Job Shadow Waiver Check-In</h1>
    </div>

    <div class="button-container">
        <button class="btn btn-primary btn-lg check-in-button" @onclick="CheckIn">Check-In</button>
    </div>
</div>

@code {
    private void CheckIn()
    {
        Navigation.NavigateTo("/checkin");
    }
}

<style>
    .landing-page {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 80vh;
        text-align: center;
        padding: 2rem;
    }

    .logo-container {
        margin-bottom: 2rem;
    }

    .logo-placeholder {
        width: 200px;
        height: 100px;
        background-color: #f0f0f0;
        border: 2px dashed #ccc;
        border-radius: 8px;
        object-fit: contain;
    }

    .title-container {
        margin-bottom: 3rem;
    }

    .main-title {
        font-size: 2.5rem;
        font-weight: bold;
        color: #333;
        margin: 0;
    }

    .button-container {
        margin-top: 1rem;
    }

    .check-in-button {
        padding: 1rem 2rem;
        font-size: 1.25rem;
        border-radius: 8px;
    }
</style>
