﻿@page "/"
@inject NavigationManager Navigation

<PageTitle>Job Shadow Waiver Check-In</PageTitle>

<div class="landing-page">
    <div class="logo-container">
        <img src="~/stc-blacklogo-2024.png" alt="Company Logo" class="img-fluid" />
    </div>

    <div class="title-container">
        <h1 class="main-title">Job Shadow Waiver Check-In</h1>
        <p>Welcome! Please review and acknowledge the following terms before beginning your job shadow.</p>
    </div>

    <div class="button-container">
        <button class="btn btn-primary btn-lg check-in-button" @onclick="CheckIn">Start</button>
    </div>
</div>

@code {
    private void CheckIn()
    {
        Navigation.NavigateTo("/checkin");
    }
}