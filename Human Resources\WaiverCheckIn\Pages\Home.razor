﻿@page "/"
@inject NavigationManager Navigation

<PageTitle>Job Shadow Waiver Check-In</PageTitle>

<div class="landing-page">
    <div class="logo-container">
        <img src="stc-whitelogo-2024.png" alt="Company Logo" class="img-fluid" />
    </div>

    <div class="title-container form-card">
        <h1 class="main-title">Job Shadow Waiver Check-In</h1>
        <h4 class="mt-3">
            Welcome! Please review and acknowledge the following terms before beginning your job shadow. To continue, you must accept all terms and fill in all required information in the waiver form.
        </h4>
        <div class="button-container">
            <button class="btn btn-primary shadow check-in-button" @onclick="CheckIn">START</button>
        </div>
    </div>

   
</div>

@code {
    private void CheckIn()
    {
        Navigation.NavigateTo("/checkin");
    }
}