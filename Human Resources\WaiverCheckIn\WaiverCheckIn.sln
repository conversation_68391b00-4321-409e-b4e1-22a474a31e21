﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36202.13 d17.14
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WaiverCheckIn", "WaiverCheckIn.csproj", "{800AC059-A6E6-47E4-BA08-6A30B21C672E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{800AC059-A6E6-47E4-BA08-6A30B21C672E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{800AC059-A6E6-47E4-BA08-6A30B21C672E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{800AC059-A6E6-47E4-BA08-6A30B21C672E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{800AC059-A6E6-47E4-BA08-6A30B21C672E}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C780C094-1D23-4E68-AD4A-F458B0E37FCB}
	EndGlobalSection
EndGlobal
