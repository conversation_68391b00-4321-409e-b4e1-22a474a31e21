using Azure.Identity;
using Blazorise;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using System;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace WaiverCheckIn.Tools
{
    public class UploadHelper
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UploadHelper> _logger;

        public UploadHelper(HttpClient httpClient, IConfiguration configuration, ILogger<UploadHelper> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// Saves the screen capture data to SharePoint folder
        /// </summary>
        /// <param name="screenCaptureData">Base64 encoded image data</param>
        /// <param name="candidateName">Name of the candidate for file naming</param>
        /// <param name="submissionDate">Date of submission</param>
        /// <returns>SharePoint file URL if successful, null if failed</returns>
        public async Task<string?> SaveScreenCaptureToSharePoint(string screenCaptureData, string candidateName, DateTime submissionDate)
        {
            try
            {
                if (string.IsNullOrEmpty(screenCaptureData))
                {
                    _logger.LogWarning("Screen capture data is empty");
                    return null;
                }

                // Remove data URL prefix if present (data:image/png;base64,)
                var base64Data = screenCaptureData;
                if (screenCaptureData.Contains(","))
                {
                    base64Data = screenCaptureData.Split(',')[1];
                }

                // Convert base64 to byte array
                var imageBytes = Convert.FromBase64String(base64Data);

                // Generate file name
                var fileName = GenerateFileName(candidateName, submissionDate);

                // Get SharePoint configuration
                var sharePointConfig = GetSharePointConfiguration();
                if (sharePointConfig == null)
                {
                    _logger.LogError("SharePoint configuration not found");
                    return null;
                }

                // Get access token
                var accessToken = await GetSharePointAccessToken(sharePointConfig);
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError("Failed to obtain SharePoint access token");
                    return null;
                }

                // Upload file to SharePoint
                var fileUrl = await UploadFileToSharePoint(imageBytes, fileName, sharePointConfig, accessToken);
                
                if (!string.IsNullOrEmpty(fileUrl))
                {
                    _logger.LogInformation($"Successfully uploaded screen capture to SharePoint: {fileUrl}");
                }

                return fileUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving screen capture to SharePoint");
                return null;
            }
        }

        private string GenerateFileName(string candidateName, DateTime submissionDate)
        {
            // Sanitize candidate name for file system
            var sanitizedName = SanitizeFileName(candidateName);
            var timestamp = submissionDate.ToString("yyyyMMdd_HHmmss");
            return $"WaiverCheckIn_{sanitizedName}_{timestamp}.png";
        }

        private string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "Unknown";

            // Remove invalid characters
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = fileName;
            
            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // Replace spaces with underscores and limit length
            sanitized = sanitized.Replace(' ', '_');
            if (sanitized.Length > 50)
            {
                sanitized = sanitized.Substring(0, 50);
            }

            return sanitized;
        }

        private SharePointConfig? GetSharePointConfiguration()
        {
            try
            {
                var config = new SharePointConfig
                {
                    SiteId = _configuration["SharePoint:SiteId"] ?? "",
                    DriveId = _configuration["SharePoint:DriveId"] ?? "",
                    FolderId = _configuration["SharePoint:FolderId"] ?? "",
                    ClientId = _configuration["SharePoint:ClientId"] ?? "",
                    ClientSecret = _configuration["SharePoint:ClientSecret"] ?? "",
                    TenantId = _configuration["SharePoint:TenantId"] ?? ""
                };

                if (string.IsNullOrEmpty(config.SiteId) || string.IsNullOrEmpty(config.DriveId) ||
                    string.IsNullOrEmpty(config.FolderId) || string.IsNullOrEmpty(config.ClientId))
                {
                    _logger.LogError("SharePoint configuration is incomplete. Required: SiteId, DriveId, FolderId, ClientId");
                    return null;
                }

                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading SharePoint configuration");
                return null;
            }
        }

        /// <summary>
        /// Creates GraphClient Object to access Sharepoint
        /// </summary>
        /// <returns> Valid Graph Client Object for API Access </returns>
        private GraphServiceClient GetClient(SharePointConfig config)
        {
            // get access with Azure Identity
            TokenCredentialOptions options = new TokenCredentialOptions
            {
                AuthorityHost = AzureAuthorityHosts.AzurePublicCloud
            };

            // Create Secret and pass values
            ClientSecretCredential clientSecretCredential = new ClientSecretCredential(
                config.TenantId, config.ClientId, config.ClientSecret, options);

            // Create Graph Client object 
            var scopes = new List<string>() { "https://graph.microsoft.com/.default" };
            GraphServiceClient graphClient = new GraphServiceClient(clientSecretCredential, scopes);

            // Return Graph Client Object
            return graphClient;
        }


        private async Task<string?> GetSharePointAccessToken(SharePointConfig config)
        {
            try
            {
                var tokenEndpoint = $"https://login.microsoftonline.com/{config.TenantId}/oauth2/v2.0/token";

                var requestBody = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("grant_type", "client_credentials"),
                    new KeyValuePair<string, string>("client_id", config.ClientId),
                    new KeyValuePair<string, string>("client_secret", config.ClientSecret),
                    new KeyValuePair<string, string>("scope", "https://graph.microsoft.com/.default")
                });

                var response = await _httpClient.PostAsync(tokenEndpoint, requestBody);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent);
                    return tokenResponse?.access_token;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Failed to get access token. Status: {response.StatusCode}, Content: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error obtaining SharePoint access token");
            }

            return null;
        }

        private async Task<string?> UploadFileToSharePoint(byte[] fileBytes, string fileName, SharePointConfig config, string accessToken)
        {
            try
            {
                //GraphServiceClient graphServiceClient = GetClient(config);

                //// resolve site/drive as needed; example: upload small file directly
                //using var ms = new MemoryStream(fileBytes);
                //var item = await graphServiceClient.Drives[config.DriveId]
                //          .Items[config.FolderId] // parent folder
                //          .ItemWithPath(fileName)
                //          .Content
                //          .PutAsync(ms);


                // Use Microsoft Graph API to upload file to specific folder
                var uploadUrl = $"https://graph.microsoft.com/v1.0/sites/{config.SiteId}/drives/{config.DriveId}/items/{config.FolderId}:/{fileName}:/content";

                using var content = new ByteArrayContent(fileBytes);
                content.Headers.ContentType = new MediaTypeHeaderValue("image/png");

                var request = new HttpRequestMessage(HttpMethod.Put, uploadUrl)
                {
                    Content = content
                };

                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var uploadResponse = JsonSerializer.Deserialize<GraphUploadResponse>(responseContent);

                    // Return the SharePoint file web URL
                    return uploadResponse?.webUrl ?? $"https://graph.microsoft.com/v1.0/sites/{config.SiteId}/drives/{config.DriveId}/items/{config.FolderId}/{fileName}";
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Failed to upload file to SharePoint via Graph API. Status: {response.StatusCode}, Content: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to SharePoint via Graph API");
            }

            return null;
        }

        // Configuration and response models
        private class SharePointConfig
        {
            public string SiteId { get; set; } = "";
            public string DriveId { get; set; } = "";
            public string FolderId { get; set; } = "";
            public string ClientId { get; set; } = "";
            public string ClientSecret { get; set; } = "";
            public string TenantId { get; set; } = "";
        }

        private class TokenResponse
        {
            public string? access_token { get; set; }
            public string? token_type { get; set; }
            public int expires_in { get; set; }
        }

        private class GraphUploadResponse
        {
            public string? id { get; set; }
            public string? name { get; set; }
            public string? webUrl { get; set; }
            public string? downloadUrl { get; set; }
        }

        private class SharePointUploadResponse
        {
            public string? ServerRelativeUrl { get; set; }
            public string? Name { get; set; }
        }
    }
}
