using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using System.Text.Json;

namespace WaiverCheckIn.Tools
{
    public class UploadHelper
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UploadHelper> _logger;

        public UploadHelper(HttpClient httpClient, IConfiguration configuration, ILogger<UploadHelper> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// Saves the screen capture data to SharePoint folder
        /// </summary>
        /// <param name="screenCaptureData">Base64 encoded image data</param>
        /// <param name="candidateName">Name of the candidate for file naming</param>
        /// <param name="submissionDate">Date of submission</param>
        /// <returns>SharePoint file URL if successful, null if failed</returns>
        public async Task<string?> SaveScreenCaptureToSharePoint(string screenCaptureData, string candidateName, DateTime submissionDate)
        {
            try
            {
                if (string.IsNullOrEmpty(screenCaptureData))
                {
                    _logger.LogWarning("Screen capture data is empty");
                    return null;
                }

                // Remove data URL prefix if present (data:image/png;base64,)
                var base64Data = screenCaptureData;
                if (screenCaptureData.Contains(","))
                {
                    base64Data = screenCaptureData.Split(',')[1];
                }

                // Convert base64 to byte array
                var imageBytes = Convert.FromBase64String(base64Data);

                // Generate file name
                var fileName = GenerateFileName(candidateName, submissionDate);

                // Get SharePoint configuration
                var sharePointConfig = GetSharePointConfiguration();
                if (sharePointConfig == null)
                {
                    _logger.LogError("SharePoint configuration not found");
                    return null;
                }

                // Get access token
                var accessToken = await GetSharePointAccessToken(sharePointConfig);
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogError("Failed to obtain SharePoint access token");
                    return null;
                }

                // Upload file to SharePoint
                var fileUrl = await UploadFileToSharePoint(imageBytes, fileName, sharePointConfig, accessToken);
                
                if (!string.IsNullOrEmpty(fileUrl))
                {
                    _logger.LogInformation($"Successfully uploaded screen capture to SharePoint: {fileUrl}");
                }

                return fileUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving screen capture to SharePoint");
                return null;
            }
        }

        private string GenerateFileName(string candidateName, DateTime submissionDate)
        {
            // Sanitize candidate name for file system
            var sanitizedName = SanitizeFileName(candidateName);
            var timestamp = submissionDate.ToString("yyyyMMdd_HHmmss");
            return $"WaiverCheckIn_{sanitizedName}_{timestamp}.png";
        }

        private string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "Unknown";

            // Remove invalid characters
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = fileName;
            
            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // Replace spaces with underscores and limit length
            sanitized = sanitized.Replace(' ', '_');
            if (sanitized.Length > 50)
            {
                sanitized = sanitized.Substring(0, 50);
            }

            return sanitized;
        }

        private SharePointConfig? GetSharePointConfiguration()
        {
            try
            {
                var config = new SharePointConfig
                {
                    SiteUrl = _configuration["SharePoint:SiteUrl"] ?? "",
                    FolderPath = _configuration["SharePoint:FolderPath"] ?? "/Shared Documents/WaiverCheckIns",
                    ClientId = _configuration["SharePoint:ClientId"] ?? "",
                    ClientSecret = _configuration["SharePoint:ClientSecret"] ?? "",
                    TenantId = _configuration["SharePoint:TenantId"] ?? ""
                };

                if (string.IsNullOrEmpty(config.SiteUrl) || string.IsNullOrEmpty(config.ClientId))
                {
                    _logger.LogError("SharePoint configuration is incomplete");
                    return null;
                }

                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading SharePoint configuration");
                return null;
            }
        }

        private async Task<string?> GetSharePointAccessToken(SharePointConfig config)
        {
            try
            {
                var tokenEndpoint = $"https://login.microsoftonline.com/{config.TenantId}/oauth2/v2.0/token";
                
                var requestBody = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("grant_type", "client_credentials"),
                    new KeyValuePair<string, string>("client_id", config.ClientId),
                    new KeyValuePair<string, string>("client_secret", config.ClientSecret),
                    new KeyValuePair<string, string>("scope", $"{config.SiteUrl}/.default")
                });

                var response = await _httpClient.PostAsync(tokenEndpoint, requestBody);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent);
                    return tokenResponse?.access_token;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Failed to get access token. Status: {response.StatusCode}, Content: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error obtaining SharePoint access token");
            }

            return null;
        }

        private async Task<string?> UploadFileToSharePoint(byte[] fileBytes, string fileName, SharePointConfig config, string accessToken)
        {
            try
            {
                // Construct the SharePoint API URL for file upload
                var uploadUrl = $"{config.SiteUrl}/_api/web/GetFolderByServerRelativeUrl('{config.FolderPath}')/Files/add(url='{fileName}',overwrite=true)";

                using var content = new ByteArrayContent(fileBytes);
                content.Headers.ContentType = new MediaTypeHeaderValue("image/png");

                var request = new HttpRequestMessage(HttpMethod.Post, uploadUrl)
                {
                    Content = content
                };

                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var uploadResponse = JsonSerializer.Deserialize<SharePointUploadResponse>(responseContent);
                    
                    // Return the SharePoint file URL
                    return $"{config.SiteUrl}{config.FolderPath}/{fileName}";
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Failed to upload file to SharePoint. Status: {response.StatusCode}, Content: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to SharePoint");
            }

            return null;
        }

        // Configuration and response models
        private class SharePointConfig
        {
            public string SiteUrl { get; set; } = "";
            public string FolderPath { get; set; } = "";
            public string ClientId { get; set; } = "";
            public string ClientSecret { get; set; } = "";
            public string TenantId { get; set; } = "";
        }

        private class TokenResponse
        {
            public string? access_token { get; set; }
            public string? token_type { get; set; }
            public int expires_in { get; set; }
        }

        private class SharePointUploadResponse
        {
            public string? ServerRelativeUrl { get; set; }
            public string? Name { get; set; }
        }
    }
}
