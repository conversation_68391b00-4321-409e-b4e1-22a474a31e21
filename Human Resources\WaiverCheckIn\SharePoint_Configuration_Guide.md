# SharePoint Configuration Guide

This guide explains how to obtain the required SharePoint IDs for the appsettings.json configuration.

## Required Configuration Values

The application requires the following SharePoint identifiers:

- **SiteId**: The unique identifier for your SharePoint site
- **DriveId**: The unique identifier for the document library/drive
- **FolderId**: The unique identifier for the specific folder where files will be uploaded
- **ClientId**: Azure AD App Registration Client ID
- **ClientSecret**: Azure AD App Registration Client Secret
- **TenantId**: Your Azure AD Tenant ID

## How to Find SharePoint IDs

### Method 1: Using Microsoft Graph Explorer (Recommended)

1. **Go to Graph Explorer**: https://developer.microsoft.com/en-us/graph/graph-explorer
2. **Sign in** with your SharePoint admin account

#### Get Site ID:
```
GET https://graph.microsoft.com/v1.0/sites/{hostname}:/sites/{site-name}
```
Example:
```
GET https://graph.microsoft.com/v1.0/sites/contoso.sharepoint.com:/sites/HumanResources
```

#### Get Drive ID:
```
GET https://graph.microsoft.com/v1.0/sites/{site-id}/drives
```

#### Get Folder ID:
```
GET https://graph.microsoft.com/v1.0/sites/{site-id}/drives/{drive-id}/root/children
```
Or for a specific folder path:
```
GET https://graph.microsoft.com/v1.0/sites/{site-id}/drives/{drive-id}/root:/WaiverCheckIns
```

### Method 2: Using PowerShell with PnP

```powershell
# Install PnP PowerShell if not already installed
Install-Module -Name PnP.PowerShell

# Connect to your SharePoint site
Connect-PnPOnline -Url "https://yourtenant.sharepoint.com/sites/yoursite" -Interactive

# Get Site ID
$site = Get-PnPSite
$site.Id

# Get Drive ID (Document Library)
$list = Get-PnPList -Identity "Documents"  # or your library name
$list.Id

# Get Folder ID
$folder = Get-PnPFolder -Url "/sites/yoursite/Shared Documents/WaiverCheckIns"
$folder.UniqueId
```

### Method 3: Using SharePoint REST API

#### Get Site ID:
```
GET https://yourtenant.sharepoint.com/sites/yoursite/_api/site/id
```

#### Get Drive/Library ID:
```
GET https://yourtenant.sharepoint.com/sites/yoursite/_api/web/lists/getbytitle('Documents')/Id
```

#### Get Folder ID:
```
GET https://yourtenant.sharepoint.com/sites/yoursite/_api/web/GetFolderByServerRelativeUrl('/sites/yoursite/Shared Documents/WaiverCheckIns')/UniqueId
```

## Azure AD App Registration

### Required Permissions:
- **Sites.ReadWrite.All** (Application permission)
- **Files.ReadWrite.All** (Application permission)

### Steps:
1. Go to Azure Portal > Azure Active Directory > App registrations
2. Create new registration or use existing
3. Go to API permissions > Add permission > Microsoft Graph
4. Add the required application permissions
5. Grant admin consent
6. Go to Certificates & secrets > Create new client secret
7. Copy the Client ID, Client Secret, and Tenant ID

## Example Configuration

```json
{
  "SharePoint": {
    "SiteId": "contoso.sharepoint.com,12345678-1234-1234-1234-123456789012,87654321-4321-4321-4321-210987654321",
    "DriveId": "b!aBcDeFgHiJkLmNoPqRsTuVwXyZ1234567890abcdefghijklmnopqrstuvwxyz",
    "FolderId": "01ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890",
    "ClientId": "12345678-1234-1234-1234-123456789012",
    "ClientSecret": "your-client-secret-value",
    "TenantId": "87654321-4321-4321-4321-210987654321"
  }
}
```

## Testing the Configuration

You can test your configuration using Graph Explorer:

```
PUT https://graph.microsoft.com/v1.0/sites/{SiteId}/drives/{DriveId}/items/{FolderId}:/test.txt:/content
Content-Type: text/plain

This is a test file
```

If successful, you should see the file appear in your SharePoint folder.
