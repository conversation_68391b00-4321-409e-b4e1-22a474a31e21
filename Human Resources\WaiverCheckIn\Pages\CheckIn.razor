@page "/checkin"
@inject NavigationManager Navigation
@using System.ComponentModel.DataAnnotations
@using System.Reflection

<PageTitle>Job Shadow Check-In Form</PageTitle>

<div class="checkin-container">
    <EditForm Model="@checkInModel" OnValidSubmit="@HandleValidSubmit" class="checkin-form">
        <DataAnnotationsValidator />
        <Microsoft.AspNetCore.Components.Forms.ValidationSummary />

        <!-- Job Shadow Waiver & Confidentiality Agreement -->
        <div class="form-card">
            <h1 class="card-title">Job Shadow Waiver & Confidentiality Agreement</h1>
            <div class="form-group">
                <label>This document confirms that the Candidate has agreed to participate in a hands-on job shadowing exercise with Southland Trailers. This is a non-paid exercise to provide understanding of the business and to assess abilities. If hired and employed for 3 months, the Candidate will receive a $130 CAD signing bonus.</label>
            </div>
            <div class="form-group mt-3">
                <div class="form-check">
                    <InputCheckbox id="understandWaiver" @bind-Value="checkInModel.UnderstandWaiver" class="form-check-input" />
                    <label for="understandWaiver" class="form-check-label">I understand the job shadow terms.</label>
                </div>
                <ValidationMessage For="@(() => checkInModel.UnderstandWaiver)" />
            </div>
        </div>

        <!-- Confidentiality Agreement -->
        <div class="form-card">
            <h4 class="card-title">Confidentiality Agreement</h4>
            <div class="form-group">
                <label>The Candidate will be exposed to Company's Confidential Information and agrees to keep it in the strictest confidence. This includes technical, business, employee, and partner information. Obligations remain even after the interview process.</label>
            </div>
            <div class="form-group mt-3">
                <div class="form-check">
                    <InputCheckbox id="understandConfidentiality" @bind-Value="checkInModel.UnderstandConfidentiality" class="form-check-input" />
                    <label for="understandConfidentiality" class="form-check-label">I agree to keep Southland Trailers' information confidential.</label>
                </div>
                <ValidationMessage For="@(() => checkInModel.UnderstandConfidentiality)" />
            </div>
        </div>

        <!-- Liability & Safety -->
        <div class="form-card">
            <h4 class="card-title">Liability & Safety</h4>
            <div class="form-group">
                <label>Southland Trailers is not responsible for injury during job shadow. WCB coverage begins only after employment.</label>
            </div>
            <div class="form-group mt-3">
                <div class="form-check">
                    <InputCheckbox id="understandLiability" @bind-Value="checkInModel.UnderstandLiability" class="form-check-input" />
                    <label for="understandLiability" class="form-check-label">I acknowledge the safety and injury terms.</label>
                </div>
                <ValidationMessage For="@(() => checkInModel.UnderstandLiability)" />
            </div>
        </div>


        <!-- Allergy & Dietary Restrictions -->
        <div class="form-card">
            <h4 class="card-title">Allergy & Dietary Restrictions</h4>
            <div class="form-group">
                <label for="allergies" class="form-label">Do you have any allergies? Please describe:</label>
                <InputTextArea id="allergies" @bind-Value="checkInModel.Allergies" class="form-control" placeholder="Please list any allergies or write 'None'" rows="3" />
                <ValidationMessage For="@(() => checkInModel.Allergies)" />
            </div>

            <div class="form-group">
                <label class="form-label">Do you carry an Epi-Pen or Benadryl?</label>
                <div class="button-group">
                    <button type="button" class="btn btn-sm @(checkInModel.HasEpipen == true ? "btn-primary" : "btn-outline-primary")"
                            @onclick="() => SetEpipenStatus(true)">Yes</button>
                    <button type="button" class="btn btn-sm @(checkInModel.HasEpipen == false ? "btn-primary" : "btn-outline-primary")"
                            @onclick="() => SetEpipenStatus(false)">No</button>
                </div>
                <ValidationMessage For="@(() => checkInModel.HasEpipen)" />
            </div>
        </div>

        <!-- Background & Reference Check -->
        <div class="form-card">
            <h4 class="card-title">Employment Information</h4>
            <div class="form-group">
                <label>I authorize Southland Trailers to contact previous employers and references regarding my work performance, duties, attendance, and other relevant information. Current employer will only be contacted with permission or upon job offer.</label>
                @* <label class="form-label">Can Southland Trailers contact your current employer?</label> *@
                <div class="button-group">
                    @foreach (CurrentEmployer value in Enum.GetValues<CurrentEmployer>())
                    {
                        <button type="button" class="btn btn-sm @(checkInModel.CanContactCurrentEmployer == value ? "btn-primary" : "btn-outline-primary")"
                                @onclick="() => SetEmployerContactStatus(value)">@GetEnumDisplayName(value)</button>
                    }
                </div>
                <ValidationMessage For="@(() => checkInModel.CanContactCurrentEmployer)" />
            </div>
            <div class="form-group mt-3">
                <div class="form-check">
                    <InputCheckbox id="understandContact" @bind-Value="checkInModel.UnderstandContact" class="form-check-input" />
                    <label for="understandContact" class="form-check-label">I authorize Southland Trailers to conduct reference checks.</label>
                </div>
                <ValidationMessage For="@(() => checkInModel.UnderstandContact)" />
            </div>
        </div>

        <!-- Sign & Submit -->
        <div class="form-card">
            <h4 class="card-title">Sign & Submit</h4>            

            <div class="form-group">
                <label for="candidateFullName" class="form-label">Candidate Full Name:</label>
                <InputText id="candidateFullName" @bind-Value="checkInModel.CandidateFullName" class="form-control" placeholder="Enter your full name" />
                <ValidationMessage For="@(() => checkInModel.CandidateFullName)" />
            </div>

            <div class="form-group">
                <label for="candidateEmail" class="form-label">Email Address:</label>
                <InputText id="candidateEmail" @bind-Value="checkInModel.CandidateEmail" class="form-control" placeholder="Enter your email address" />
                <ValidationMessage For="@(() => checkInModel.CandidateEmail)" />
            </div>

            <div class="form-group">
                <label for="candidatePhoneNumber" class="form-label">Phone Number:</label>
                <InputText id="candidatePhoneNumber" @bind-Value="checkInModel.CandidatePhoneNumber" class="form-control" placeholder="Enter your phone number" />
                <ValidationMessage For="@(() => checkInModel.CandidatePhoneNumber)" />
            </div>

            <div class="form-actions d-flex justify-content-between">
                <button type="button" class="btn shadow btn-secondary" @onclick="GoBack">Cancel</button>
                <button type="submit" class="btn shadow btn-primary">Submit & Check In</button>
            </div>
        </div>
    </EditForm>
</div>

@code {
    private CheckInModel checkInModel = new CheckInModel();

    private void HandleValidSubmit()
    {
        // Handle form submission here
        // For now, just show a simple alert or navigate to a confirmation page
        Console.WriteLine("Form submitted successfully!");
        // You can add logic here to save the data or navigate to a confirmation page
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/");
    }

    private string GetEnumDisplayName(CurrentEmployer value)
    {
        var field = value.GetType().GetField(value.ToString());
        var attribute = field?.GetCustomAttribute<DisplayAttribute>();
        return attribute?.Name ?? value.ToString();
    }

    private void SetEpipenStatus(bool hasEpipen)
    {
        checkInModel.HasEpipen = hasEpipen;
    }

    private void SetEmployerContactStatus(CurrentEmployer status)
    {
        checkInModel.CanContactCurrentEmployer = status;
    }

    public class CheckInModel
    {
        [Required(ErrorMessage = "Full name is required")]
        public string CandidateFullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email address is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string CandidateEmail { get; set; } = string.Empty;

        [Required(ErrorMessage = "Phone number is required")]
        public string CandidatePhoneNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Please specify allergies or write 'None'")]
        public string Allergies { get; set; } = string.Empty;

        public bool? HasEpipen { get; set; }

        [Required(ErrorMessage = "Please select an option")]
        public CurrentEmployer? CanContactCurrentEmployer { get; set; }

        [Required(ErrorMessage = "You must acknowledge understanding of the waiver")]
        public bool UnderstandWaiver { get; set; } = false;

        [Required(ErrorMessage = "You must acknowledge understanding of the confidentiality agreement")]
        public bool UnderstandConfidentiality { get; set; } = false;

        [Required(ErrorMessage = "You must acknowledge understanding of liability and safety")]
        public bool UnderstandLiability { get; set; } = false;

        [Required(ErrorMessage = "You must acknowledge understanding of contact information requirements")]
        public bool UnderstandContact { get; set; } = false;
    }
}


