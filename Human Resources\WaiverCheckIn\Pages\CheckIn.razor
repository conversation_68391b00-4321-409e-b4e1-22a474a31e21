@page "/checkin"
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@using System.ComponentModel.DataAnnotations
@using System.Reflection

<PageTitle>Check-In Form</PageTitle>

<div class="checkin-container">
    <EditForm Model="@checkInModel" OnValidSubmit="@HandleValidSubmit" class="checkin-form">
        <DataAnnotationsValidator />

        <!-- Job Shadow Waiver & Confidentiality Agreement -->
        <div class="form-card">
            <h1 class="card-title">Job Shadow Waiver & Confidentiality Agreement</h1>
            <div class="form-group">
                <label>This document confirms that the Candidate has agreed to participate in a hands-on job shadowing exercise with Southland Trailers. This is a non-paid exercise to provide understanding of the business and to assess abilities. If hired and employed for 3 months, the Candidate will receive a $130 CAD signing bonus.</label>
            </div>
            <div class="form-group mt-3">
                <div class="form-check">
                    <InputCheckbox id="understandWaiver" @bind-Value="checkInModel.UnderstandWaiver" class="form-check-input" />
                    <label for="understandWaiver" class="form-check-label">I understand the job shadow terms.</label>
                </div>
                <ValidationMessage For="@(() => checkInModel.UnderstandWaiver)" />
            </div>
        </div>

        <!-- Confidentiality Agreement -->
        <div class="form-card">
            <h4 class="card-title">Confidentiality Agreement</h4>
            <div class="form-group">
                <div class="confidentiality-text">
                    <p class="@(showFullConfidentiality ? "" : "text-preview")">
                        This agreement (the "Agreement") is entered into by Southland Trailer Corp ("Company") and the Candidate. The Candidate and Company agrees as follows:

                        @if (showFullConfidentiality)
                        {
                            <span class="full-text">
                                <br/>
                                <u>Company's Trade Secrets</u><br/>
                                The Candidate will be exposed to Company's Confidential Information. "Confidential Information" means information or material that is commercially valuable to Company and not generally known or readily ascertainable in the industry. This includes, but is not limited to:<br/>
                                <ol type="a">
                                    <li>
                                        technical information concerning Company's products and services, including product know-how, formulas, designs, devices, diagrams, software code, test results, processes, inventions, research projects and product development, technical memoranda and correspondence;
                                    </li>
                                    <li>information concerning Company's business, including cost information, profits, sales information, accounting and unpublished financial information, business plans, markets and marketing methods, customer lists and customer information, purchasing techniques, supplier lists and supplier information and advertising strategies;</li>
                                    <li>Information concerning Company's employees including salaries, strengths, weaknesses and skills;</li>
                                    <li>information submitted by Company's customers, suppliers, employees, consultants or co-venture partners with Company for study, evaluation or use; and</li>
                                    <li>Any other information not generally known to the public which, if misused or disclosed, could reasonably be expected to adversely affect Company's business.</li>
                                </ol><br />
                                <u>Nondisclosure of Trade Secrets</u><br />
                                The Candidate shall keep Company's Confidential Information, in the strictest confidence. The Candidate will not disclose such information to anyone outside Company without Company's prior written consent. Nor will The Candidate make use of any Confidential Information for The Candidate e own purposes or the benefit of anyone other than Company.<br />
                                However, The Candidate shall have no obligation to treat as confidential any information which:<br />
                                <ol type="a">
                                    <li>Was in The Candidate possession or known to The Candidate, without an obligation to keep it confidential, before such information was disclosed to The Candidate by Company;</li>
                                    <li>Is or becomes public knowledge through a source other than The Candidate and through no fault of the Candidate or</li>
                                    <li>Is or becomes lawfully available to The Candidate from a source other than Company.</li>
                                </ol><br />
                                <u>Confidentiality Obligation Survives Interview Process</u><br />
                                The Candidate obligation to maintain the confidentiality and security of Confidential Information remains even after The Candidate interview with Company ends and continues for so long as such Confidential Information remains a trade secret.<br /><br />
                                <u>General Provisions</u><br />
                                <ol type="a">
                                    <li><u>Severability</u>: If a court finds any provision of this Agreement invalid or unenforceable, the remainder of this Agreement shall be interpreted so as best to affect the intent of Company and the Candidate.</li>
                                    <li><u>Integration</u>: This Agreement expresses the complete understanding of the parties with respect to the subject matter and supersedes all prior proposals, agreements, representations and understandings. This Agreement may not be amended except in a writing signed by both Company and The Candidate.</li>
                                    <li><u>Waiver</u>: The failure to exercise any right provided in this Agreement shall not be a waiver of prior or subsequent rights.</li>
                                </ol>
                            </span>
                        }
                    </p>
                    <button type="button" class="btn btn-link see-more-btn" @onclick="ToggleConfidentialityText">
                        @(showFullConfidentiality ? "See less..." : "See more...")
                    </button>
                </div>
            </div>
            <div class="form-group mt-3">
                <div class="form-check">
                    <InputCheckbox id="understandConfidentiality" @bind-Value="checkInModel.UnderstandConfidentiality" class="form-check-input" />
                    <label for="understandConfidentiality" class="form-check-label">I agree to keep Southland Trailers' information confidential.</label>
                </div>
                <ValidationMessage For="@(() => checkInModel.UnderstandConfidentiality)" />
            </div>
        </div>

        <!-- Liability & Safety -->
        <div class="form-card">
            <h4 class="card-title">Liability & Safety</h4>
            <div class="form-group">
                <label>Southland Trailers is not responsible for injury during job shadow. You must be employed after the job shadow process to be entitled to claim under WCB.</label>
            </div>
            <div class="form-group mt-3">
                <div class="form-check">
                    <InputCheckbox id="understandLiability" @bind-Value="checkInModel.UnderstandLiability" class="form-check-input" />
                    <label for="understandLiability" class="form-check-label">I acknowledge the safety and injury terms.</label>
                </div>
                <ValidationMessage For="@(() => checkInModel.UnderstandLiability)" />
            </div>
        </div>


        <!-- Allergy or Dietary Restrictions -->
        <div class="form-card">
            <h4 class="card-title">Allergy or Dietary Restrictions</h4>
            <div class="form-group">
                <label for="allergies" class="form-label">Do you have any allergies? Please describe:</label>
                <InputTextArea id="allergies" @bind-Value="checkInModel.Allergies" class="form-control" placeholder="Please list any allergies or dietary restrictions" rows="3" />
                <ValidationMessage For="@(() => checkInModel.Allergies)" />
            </div>

            <div class="form-group">
                <label class="form-label">Do you carry an epi-pen or Benadryl?</label>
                <div class="button-group">
                    <button type="button" class="btn @(checkInModel.HasEpipen == true ? "btn-primary" : "btn-outline-primary")"
                            @onclick="() => SetEpipenStatus(true)">Yes</button>
                    <button type="button" class="btn @(checkInModel.HasEpipen == false ? "btn-primary" : "btn-outline-primary")"
                            @onclick="() => SetEpipenStatus(false)">No</button>
                </div>
                <ValidationMessage For="@(() => checkInModel.HasEpipen)" />
            </div>
        </div>

        <!-- Background & Reference Check -->
        <div class="form-card">
            <h4 class="card-title">Employment Information</h4>
            <div class="form-group">
                <label>I authorize Southland Trailers to contact past employers and references regarding my work performance, duties, attendance, and other relevant information. Current employer will only be contacted with my permission or upon job offer.</label>
                <label class="form-label">Can Southland Trailers contact your current employer?</label>
                <div class="button-group">
                    @foreach (CurrentEmployer value in Enum.GetValues<CurrentEmployer>())
                    {
                        <button type="button" class="btn @(checkInModel.CanContactCurrentEmployer == value ? "btn-primary" : "btn-outline-primary")"
                                @onclick="() => SetEmployerContactStatus(value)">@GetEnumDisplayName(value)</button>
                    }
                </div>
                <ValidationMessage For="@(() => checkInModel.CanContactCurrentEmployer)" />
            </div>
            <div class="form-group mt-3">
                <div class="form-check">
                    <InputCheckbox id="understandContact" @bind-Value="checkInModel.UnderstandContact" class="form-check-input" />
                    <label for="understandContact" class="form-check-label">I authorize Southland Trailers to conduct reference checks.</label>
                </div>
                <ValidationMessage For="@(() => checkInModel.UnderstandContact)" />
            </div>
        </div>

        <!-- Contact Information Card -->
        <div class="form-card">
            <h4 class="card-title">Contact Information</h4>

            <div class="form-group">
                <label for="candidateFullName" class="form-label">Candidate Full Name:</label>
                <InputText id="candidateFullName" @bind-Value="checkInModel.CandidateFullName" class="form-control" placeholder="Enter your full name" />
                <ValidationMessage For="@(() => checkInModel.CandidateFullName)" />
            </div>

            <div class="form-group">
                <label for="candidateEmail" class="form-label">Email Address:</label>
                <InputText id="candidateEmail" @bind-Value="checkInModel.CandidateEmail" class="form-control" placeholder="Enter your email address" />
                <ValidationMessage For="@(() => checkInModel.CandidateEmail)" />
            </div>

            <div class="form-group">
                <label for="candidatePhoneNumber" class="form-label">Phone Number:</label>
                <InputText id="candidatePhoneNumber" @bind-Value="checkInModel.CandidatePhoneNumber" class="form-control" placeholder="Enter your phone number" />
                <ValidationMessage For="@(() => checkInModel.CandidatePhoneNumber)" />
            </div>

            <div class="form-group">
                <label class="form-label">Date:</label>
                <div class="date-display">@DateTime.Today.ToString("MMMM dd, yyyy")</div>
            </div>

            <div class="form-group">
                <label class="form-label">Signature:</label>
                <div class="signature-container">
                    <SignaturePad @ref="signaturePad"
                                  @bind-Value="@signatureData"
                                  BackgroundColor="#ffffff"
                                  PenColor="#000000"
                                  CanvasWidth="400"
                                  CanvasHeight="200"
                                  class="signature-pad" />
                    <div class="signature-placeholder">Sign here</div>
                    <div class="signature-actions">
                        <button type="button" class="btn btn-sm" @onclick="ClearSignature">Clear</button>
                    </div>
                </div>
               
                <ValidationMessage For="@(() => checkInModel.SignatureData)" />
            </div>

            <div class="form-actions d-flex justify-content-between">
                <button type="button" class="btn btn-secondary" @onclick="GoBack">Cancel</button>
                <button type="submit" class="btn btn-primary">Submit & Sign</button>
            </div>

            <div class="mt-3"><Microsoft.AspNetCore.Components.Forms.ValidationSummary /></div>
        </div>
    </EditForm>
</div>

@code {



    private CheckInModel checkInModel = new CheckInModel();
    private SignaturePad signaturePad = new();
    private bool showFullConfidentiality = false;

    byte[] signatureData = null;
    string Image64 => SignaturePad.GetDataUrl(signatureData);

    private async Task HandleValidSubmit()
    {
        try
        {
            // Get signature data before submitting
            if (signaturePad != null)
            {

                checkInModel.SignatureData = Image64;
            }

            // Capture screen with all inputs
            var screenCapture = await CaptureScreen();
            checkInModel.ScreenCaptureData = screenCapture;

            // Handle form submission here
            Console.WriteLine("Form submitted successfully!");
            Console.WriteLine($"Signature data length: {checkInModel.SignatureData?.Length ?? 0}");
            Console.WriteLine($"Screen capture data length: {checkInModel.ScreenCaptureData?.Length ?? 0}");


            string folder = Path.Combine(Directory.GetCurrentDirectory(), "sig");
            SaveBase64Image(screenCapture, @"//zeus/afs/Backup Load Config/test", "testScreenCap");

            // You can add logic here to save the data or navigate to a confirmation page
            // For example: await SaveFormData(checkInModel);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during form submission: {ex.Message}");
            // Handle error appropriately
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/");
    }

    private string GetEnumDisplayName(CurrentEmployer value)
    {
        var field = value.GetType().GetField(value.ToString());
        var attribute = field?.GetCustomAttribute<DisplayAttribute>();
        return attribute?.Name ?? value.ToString();
    }

    private void SetEpipenStatus(bool hasEpipen)
    {
        checkInModel.HasEpipen = hasEpipen;
    }

    private void SetEmployerContactStatus(CurrentEmployer status)
    {
        checkInModel.CanContactCurrentEmployer = status;
    }

    private async Task ClearSignature()
    {
        if (signaturePad != null)
        {
            await signaturePad.Clear();
            checkInModel.SignatureData = string.Empty;
        }
    }

    private void ToggleConfidentialityText()
    {
        showFullConfidentiality = !showFullConfidentiality;
    }

    private async Task<string> CaptureScreen()
    {
        try
        {
            // Use JavaScript to capture the screen/form area
            var screenCapture = await JSRuntime.InvokeAsync<string>("captureFormScreen");
            return screenCapture ?? string.Empty;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error capturing screen: {ex.Message}");
            return string.Empty;
        }
    }

    private string SaveBase64Image(string base64String, string folderPath, string fileNameWithoutExt)
    {
        try
        {
            // Ensure the folder exists
            if (!Directory.Exists(folderPath))
                Directory.CreateDirectory(folderPath);

            // Remove the prefix if it has one (e.g. "data:image/png;base64,")
            var base64Data = base64String.Contains(",")
                ? base64String.Substring(base64String.IndexOf(",") + 1)
                : base64String;

            // Decode base64 string
            byte[] imageBytes = Convert.FromBase64String(base64Data);

            // Create file path
            string filePath = Path.Combine(folderPath, fileNameWithoutExt + ".png");

            // Save as file
            File.WriteAllBytes(filePath, imageBytes);

            return filePath; // return path of saved image
        }
        catch (Exception ex)
        {
            throw new Exception("Error saving base64 image", ex);
        }
    }

    public class CheckInModel
    {
        [Required(ErrorMessage = "Please select an option")]
        public CurrentEmployer? CanContactCurrentEmployer { get; set; }

        [AllowedValues(true, ErrorMessage = "You must acknowledge understanding of the waiver")]
        public bool UnderstandWaiver { get; set; } = false;

        [AllowedValues(true, ErrorMessage = "You must acknowledge understanding of the confidentiality agreement")]
        public bool UnderstandConfidentiality { get; set; }

        [AllowedValues(true, ErrorMessage = "You must acknowledge understanding of liability and safety")]
        public bool UnderstandLiability { get; set; }

        public string Allergies { get; set; } = string.Empty;

        [Required(ErrorMessage = "Please select an option")]
        public bool? HasEpipen { get; set; }

        [AllowedValues(true, ErrorMessage = "You must acknowledge understanding of contact information requirements")]
        public bool UnderstandContact { get; set; }

        [Required(ErrorMessage = "Full name is required")]
        public string CandidateFullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email address is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string CandidateEmail { get; set; } = string.Empty;

        [Required(ErrorMessage = "Phone number is required")]
        public string CandidatePhoneNumber { get; set; } = string.Empty;

        // [Required(ErrorMessage = "Signature is required")] //TODO
        public string SignatureData { get; set; } = string.Empty;

        public string ScreenCaptureData { get; set; } = string.Empty;
    }
}