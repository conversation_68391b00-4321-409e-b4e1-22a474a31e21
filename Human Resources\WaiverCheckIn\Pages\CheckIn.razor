@page "/checkin"
@inject NavigationManager Navigation
@using System.ComponentModel.DataAnnotations
@using System.Reflection

<PageTitle>Check-In Form</PageTitle>

<div class="checkin-container">
    <h2 class="form-title">Job Shadow Waiver Check-In Form</h2>
    
    <EditForm Model="@checkInModel" OnValidSubmit="@HandleValidSubmit" class="checkin-form">
        <DataAnnotationsValidator />
        <ValidationSummary />

        <div class="form-group">
            <label for="candidateFullName" class="form-label">Full Name:</label>
            <InputText id="candidateFullName" @bind-Value="checkInModel.CandidateFullName" class="form-control" placeholder="Enter your full name" />
            <ValidationMessage For="@(() => checkInModel.CandidateFullName)" />
        </div>

        <div class="form-group">
            <label for="candidateEmail" class="form-label">Email Address:</label>
            <InputText id="candidateEmail" @bind-Value="checkInModel.CandidateEmail" class="form-control" placeholder="Enter your email address" />
            <ValidationMessage For="@(() => checkInModel.CandidateEmail)" />
        </div>

        <div class="form-group">
            <label for="candidatePhoneNumber" class="form-label">Phone Number:</label>
            <InputText id="candidatePhoneNumber" @bind-Value="checkInModel.CandidatePhoneNumber" class="form-control" placeholder="Enter your phone number" />
            <ValidationMessage For="@(() => checkInModel.CandidatePhoneNumber)" />
        </div>

        <div class="form-group">
            <label for="allergies" class="form-label">Allergies:</label>
            <InputTextArea id="allergies" @bind-Value="checkInModel.Allergies" class="form-control" placeholder="Please list any allergies or write 'None'" rows="3" />
            <ValidationMessage For="@(() => checkInModel.Allergies)" />
        </div>

        <div class="form-group">
            <div class="form-check">
                <InputCheckbox id="hasEpipen" @bind-Value="checkInModel.HasEpipen" class="form-check-input" />
                <label for="hasEpipen" class="form-check-label">I carry an EpiPen</label>
            </div>
            <ValidationMessage For="@(() => checkInModel.HasEpipen)" />
        </div>

        <div class="form-group">
            <label for="canContactCurrentEmployer" class="form-label">Can we contact your current employer?</label>
            <InputSelect id="canContactCurrentEmployer" @bind-Value="checkInModel.CanContactCurrentEmployer" class="form-control">
                <option value="">Please select...</option>
                @foreach (CurrentEmployer value in Enum.GetValues<CurrentEmployer>())
                {
                    <option value="@value">@GetEnumDisplayName(value)</option>
                }
            </InputSelect>
            <ValidationMessage For="@(() => checkInModel.CanContactCurrentEmployer)" />
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary btn-lg">Submit Check-In</button>
            <button type="button" class="btn btn-secondary btn-lg" @onclick="GoBack">Back to Home</button>
        </div>
    </EditForm>
</div>

@code {
    private CheckInModel checkInModel = new CheckInModel();

    private void HandleValidSubmit()
    {
        // Handle form submission here
        // For now, just show a simple alert or navigate to a confirmation page
        Console.WriteLine("Form submitted successfully!");
        // You can add logic here to save the data or navigate to a confirmation page
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/");
    }

    private string GetEnumDisplayName(CurrentEmployer value)
    {
        var field = value.GetType().GetField(value.ToString());
        var attribute = field?.GetCustomAttribute<DisplayAttribute>();
        return attribute?.Name ?? value.ToString();
    }

    public class CheckInModel
    {
        [Required(ErrorMessage = "Full name is required")]
        public string CandidateFullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email address is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string CandidateEmail { get; set; } = string.Empty;

        [Required(ErrorMessage = "Phone number is required")]
        public string CandidatePhoneNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "Please specify allergies or write 'None'")]
        public string Allergies { get; set; } = string.Empty;

        public bool HasEpipen { get; set; } = false;

        [Required(ErrorMessage = "Please select an option")]
        public CurrentEmployer? CanContactCurrentEmployer { get; set; }
    }
}


